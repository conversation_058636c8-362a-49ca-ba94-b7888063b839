<template>
  <div class="mobile-container">
    <!-- 头部信息 -->
    <mobile-search-bar type="backHeader"></mobile-search-bar>

    <!-- 热搜词 -->
    <div
      class="w-full py-[0.24rem] px-[0.24rem]"
      v-if="pageData.hotKeywords && pageData.hotKeywords.length"
    >
      <n-text class="text-[0.32rem] leading-[0.48rem] font-medium"
        >{{ authStore.i18n("cm_home.frequentlySearched") }}：</n-text
      >
      <div class="flex flex-wrap items-center">
        <a
          v-for="(item, index) in pageData.hotKeywords"
          :key="index"
          data-spm-box="search-hot-keywords"
          :href="`/h5/search/list?keyword=${encodeURIComponent(item)}`"
          ><n-button
            strong
            secondary
            class="mr-[0.32rem] leading-4 mt-[0.32rem]"
          >
            <n-ellipsis :line-clamp="1">
              {{ item }}
            </n-ellipsis>
          </n-button></a
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();

const pageData = reactive({
  hotKeywords: <any>[],
});

onPageData();
async function onPageData() {
  const res: any = await useHomePageData({});
  if (res?.result?.code === 200) {
    pageData.hotKeywords = res?.data?.hotKeywords;
  }
}
</script>
<style scoped lang="scss">
.mobile-container {
  padding-top: 1.12rem;
}
</style>
