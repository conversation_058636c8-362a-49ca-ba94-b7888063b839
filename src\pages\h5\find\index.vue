<template>
  <div
    class="mobile-container"
    :class="pageData.showTabs && pageData.isTabsSticky ? 'pt-[1.16rem]' : ''"
  >
    <!-- Tab切换区域 -->
    <div
      v-if="pageData.showTabs"
      ref="tabsRef"
      class="bg-white"
      :class="{
        'fixed top-0 left-0 right-0 z-2': pageData.isTabsSticky,
      }"
    >
      <div
        class="bg-[#F2F2F2] mx-[0.16rem] mt-[0.16rem] rounded-[0.4rem] p-[0.08rem] h-[1.16rem] mb-[0.16rem] transition-all duration-300"
      >
        <div class="flex">
          <div
            v-if="pageData.onlineOrderCartTab"
            @click="switchTab('direct')"
            :class="[
              'flex-1 flex items-center justify-center py-[0.26rem] px-[0.28rem] rounded-[0.32rem]  transition-all duration-300 gap-[0.12rem] relative',
              pageData.activeTab === 'direct' ? 'bg-[#FFF]' : 'bg-transparent',
            ]"
          >
            <img
              src="@/assets/icons/find/payment.svg"
              alt="payment"
              class="w-[0.48rem] h-[0.48rem]"
            />
            <span class="text-[0.32rem] leading-[0.32rem]">
              {{ authStore.i18n("cm_find.directPaymentGoods") }}
            </span>
            <div
              class="w-[0.36rem] h-[0.36rem] rounded-full bg-[#e50113] text-[0.24rem] leading-[0.24rem] absolute top-[-0.18rem] right-[0] flex items-center justify-center text-[#fff]"
            >
              {{ pageData.onlineOrderCartTab.stat?.goodsCount || 0 }}
            </div>
          </div>

          <div
            v-if="pageData.goodsLookingCartTab"
            @click="switchTab('quotation')"
            :class="[
              'flex-1 flex items-center justify-center py-[0.26rem] px-[0.28rem] rounded-[0.32rem]  transition-all duration-300 gap-[0.12rem] relative',
              pageData.activeTab === 'quotation'
                ? 'bg-[#FFF]'
                : 'bg-transparent',
            ]"
          >
            <img
              src="@/assets/icons/find/quotation.svg"
              alt="quotation"
              class="w-[0.48rem] h-[0.48rem]"
            />
            <span class="text-[0.32rem] leading-[0.32rem]">
              {{ authStore.i18n("cm_find.inquiryGoods") }}
            </span>
            <span
              class="w-[0.36rem] h-[0.36rem] rounded-full bg-[#e50113] text-[0.24rem] leading-[0.24rem] absolute top-[-0.18rem] right-[0] flex items-center justify-center text-[#fff]"
            >
              {{ pageData.goodsLookingCartTab.stat?.goodsCount || 0 }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div v-if="!pageData.loading" class="content-section">
      <!-- Tab内容 -->
      <div v-if="pageData.showTabs">
        <!-- 直接付款商品Tab -->
        <DirectPaymentTab
          v-if="pageData.activeTab === 'direct' && pageData.onlineOrderCartTab"
          :cartData="pageData.onlineOrderCartTab"
          @onAllSelection="onAllSelection"
          @onGoodsSelection="onGoodsSelection"
          @onSkuSelection="onSkuSelection"
          @onCartQtyUpdate="onCartQtyUpdate"
          @onDeleteGoods="onDeleteGoods"
          @onDeleteSku="onDeleteSku"
          @onOpenSkuDialog="onOpenSkuDialog"
          :showTabs="pageData.showTabs"
        />

        <!-- 询价商品Tab -->
        <QuotationRequiredTab
          v-if="
            pageData.activeTab === 'quotation' && pageData.goodsLookingCartTab
          "
          :cartData="pageData.goodsLookingCartTab"
          @onAllSelection="onAllSelection"
          @onGoodsSelection="onGoodsSelection"
          @onSkuSelection="onSkuSelection"
          @onCartQtyUpdate="onCartQtyUpdate"
          @onDeleteGoods="onDeleteGoods"
          @onDeleteSku="onDeleteSku"
          @onOpenSkuDialog="onOpenSkuDialog"
          :showTabs="pageData.showTabs"
        />
      </div>

      <!-- 无Tab时的内容 -->
      <div v-else>
        <!-- 直接付款商品 -->
        <DirectPaymentTab
          v-if="pageData.onlineOrderCartTab"
          :cartData="pageData.onlineOrderCartTab"
          @onAllSelection="onAllSelection"
          @onGoodsSelection="onGoodsSelection"
          @onSkuSelection="onSkuSelection"
          @onCartQtyUpdate="onCartQtyUpdate"
          @onDeleteGoods="onDeleteGoods"
          @onDeleteSku="onDeleteSku"
          @onOpenSkuDialog="onOpenSkuDialog"
          :showTabs="pageData.showTabs"
        />

        <!-- 询价商品 -->
        <QuotationRequiredTab
          v-if="pageData.activeTab === 'quotation'"
          :cartData="pageData.goodsLookingCartTab"
          @onAllSelection="onAllSelection"
          @onGoodsSelection="onGoodsSelection"
          @onSkuSelection="onSkuSelection"
          @onCartQtyUpdate="onCartQtyUpdate"
          @onDeleteGoods="onDeleteGoods"
          @onDeleteSku="onDeleteSku"
          @onOpenSkuDialog="onOpenSkuDialog"
          :showTabs="pageData.showTabs"
        />
      </div>
    </div>

    <div v-show="pageData.loading" class="loading-overlay">
      <n-spin stroke="#e50113" :show="pageData.loading"> </n-spin>
    </div>

    <mobile-tab-bar :naiveBar="3" />

    <n-drawer
      v-model:show="pageData.dialogVisible"
      resizable
      default-width="100%"
      default-height="90%"
      placement="bottom"
      :on-after-leave="onCancel"
    >
      <n-drawer-content>
        <template #header></template>
        <!-- 商品规格 -->
        <div>
          <div
            v-for="spec in pageData.currentGoods.specList"
            :key="spec.id"
            class="ml-[0.16rem] text-[0.28rem]"
          >
            <div class="mb-[0.08rem] font-medium">{{ spec.name }}:</div>
            <div class="flex flex-wrap mb-[0.16rem]">
              <div
                v-for="item in spec.items"
                :key="item.itemId"
                @click="onSpecUpdate(spec.id, item.itemId, item)"
                class="spec-btn min-w-[0.8rem] max-w-4/5 relative"
                :class="{
                  'current-btn': pageData.selectedSpec[spec?.id] == item.itemId,
                  'disabled-btn': item.disabled,
                }"
              >
                <n-image
                  lazy
                  preview-disabled
                  object-fit="fill"
                  class="w-[0.72rem] h-[0.72rem] shrink-0 mr-[0.24rem]"
                  :src="item.imageUrl"
                  v-if="item.imageUrl"
                  :img-props="{ referrerpolicy: 'no-referrer' }"
                />
                <span class="py-[0.16rem]">{{ item.itemName }}</span>
                <icon-card
                  size="20"
                  name="typcn:tick"
                  color="#fff"
                  class="btn-tick mr-[0.16rem]"
                ></icon-card>
              </div>
            </div>
          </div>
        </div>
        <template #footer
          ><div class="flex w-full">
            <n-button
              @click="onConfirm"
              color="#E50113"
              class="mr-[0.32rem] flex-1 text-[0.28rem] h-[0.68rem]"
              >{{ authStore.i18n("cm_find.confirm") }}</n-button
            >
            <n-button
              @click="onCancel"
              class="flex-1 text-[0.28rem] h-[0.68rem]"
              >{{ authStore.i18n("cm_find.cancel") }}</n-button
            >
          </div></template
        >
      </n-drawer-content>
    </n-drawer>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import DirectPaymentTab from "./components/DirectPaymentTab.vue";
import QuotationRequiredTab from "./components/QuotationRequiredTab.vue";

const message = useMessage();
const authStore = useAuthStore();
const tabsRef = ref<HTMLElement>();
const pageData = reactive<any>({
  activeTab: "direct", // 'direct' | 'quotation'
  showTabs: false,
  onlineOrderCartTab: null, // 直接付款商品Tab数据
  goodsLookingCartTab: null, // 询价商品Tab数据
  currentCartData: null, // 当前显示的购物车数据
  currentSku: <any>{},
  updatedSku: <any>{},
  currentGoods: <any>{},
  selectedSpec: <any>{},
  loading: false,
  dialogVisible: false,
  isTabsSticky: false,
});

watch(
  () => pageData.selectedSpec,
  (newVal: any) => {
    // 多组规格时 选中倒数第二组的时候 需要检验sku的库存以及上下架状态
    if (
      newVal &&
      pageData.currentGoods?.specList?.length > 1 &&
      Object.values(newVal).length > pageData.currentGoods?.specList.length - 2
    ) {
      const targetId =
        pageData.currentGoods?.specList[
          pageData.currentGoods?.specList?.length - 2
        ].id;
      for (const id in pageData.selectedSpec) {
        if (targetId == id) {
          updateSpecStatus();
          break;
        }
      }
    } else {
      //只有一组时 直接校验sku的库存以及上下架状态
      updateOneSpecStatus();
    }
  },
  { deep: true }
);

// 初始化页面数据
onGetCartByTab("init");

// 获取购物车列表（按Tab分割）
async function onGetCartByTab(type?: string) {
  if (type === "init") {
    pageData.loading = true;
  }
  const res: any = await useGetCartByTab({});
  if (type === "init") {
    pageData.loading = false;
  }

  if (res?.result?.code === 200) {
    const data = res?.data;
    onProcessCartData(data, type === "init");
  } else if (res?.result?.code === 403) {
    navigateToPage(
      `/h5/user/login`,
      { pageSource: window.location.href },
      false
    );
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

function onProcessCartData(data: any, isInit: boolean = false) {
  if (!data) return;

  pageData.onlineOrderCartTab = data?.onlineOrderCartTab || null;
  pageData.goodsLookingCartTab = data?.goodsLookingCartTab || null;

  const hasDirectPayment = !!pageData.onlineOrderCartTab;
  const hasQuotation = !!pageData.goodsLookingCartTab;

  const currentActiveTab = pageData.activeTab;

  if (hasDirectPayment && hasQuotation) {
    // 两种商品都有，显示Tab
    pageData.showTabs = true;

    if (isInit || !currentActiveTab) {
      pageData.activeTab = "direct";
    }

    if (pageData.activeTab === "direct") {
      pageData.currentCartData = pageData.onlineOrderCartTab;
    } else {
      pageData.currentCartData = pageData.goodsLookingCartTab;
    }
  } else if (hasDirectPayment) {
    pageData.showTabs = false;
    pageData.activeTab = "direct";
    pageData.currentCartData = pageData.onlineOrderCartTab;
  } else if (hasQuotation) {
    pageData.showTabs = false;
    pageData.activeTab = "quotation";
    pageData.currentCartData = pageData.goodsLookingCartTab;
  } else {
    // 两种商品都没有时，默认显示询盘商品tab
    pageData.showTabs = false;
    pageData.activeTab = "quotation";
    pageData.currentCartData = null;
  }

  if (pageData.currentCartData?.goodsList) {
    onProcessGoodsData(pageData.currentCartData.goodsList);
  }

  // 同步更新store的购物车数据
  authStore.getCartList(data);
}

// 处理商品数据
function onProcessGoodsData(goodsList: any[]) {
  goodsList.forEach((goods: any) => {
    goods.selected = goods.skuList.every((obj: any) => obj.selected);
    goods.skuTotalQuantity = 0;
    goods.skuSelectedQuantity = 0;

    goods.skuList.forEach((sku: any) => {
      if (!sku.minIncreaseQuantity) {
        sku.minIncreaseQuantity = goods.minIncreaseQuantity;
      }
      goods.skuTotalQuantity += sku.buyQty;
      if (sku.selected) {
        goods.skuSelected = true;
        goods.skuSelectedQuantity += sku.buyQty;
      }
    });
  });
}

// Tab切换
function switchTab(tab: "direct" | "quotation") {
  pageData.activeTab = tab;

  if (tab === "direct") {
    pageData.currentCartData = pageData.onlineOrderCartTab;
  } else {
    pageData.currentCartData = pageData.goodsLookingCartTab;
  }
}

// 全选/全不选
async function onAllSelection(value: any, supportOnlineOrder?: boolean) {
  let isOnlineOrder = supportOnlineOrder;
  if (isOnlineOrder === undefined) {
    isOnlineOrder = pageData.activeTab === "direct";
  }

  const res: any = await useUpdateSelectedAll({
    supportOnlineOrder: isOnlineOrder,
    selected: value,
  });
  if (res?.result?.code === 200) {
    onProcessCartData(res?.data, false);
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

// 商品选中
function onGoodsSelection(value: any, goods: any) {
  onUpdateCart({ selected: value, goodsId: goods.goodsId, padc: goods.padc });
}

// sku选中
function onSkuSelection(value: any, sku: any, goods: any) {
  onUpdateCart({ selected: value, skuId: sku.skuId, padc: sku.padc });
}

// sku数量修改
function onCartQtyUpdate(value: any, sku: any, goods: any) {
  onUpdateCart({
    quantity: value,
    selected: sku.selected,
    skuId: sku.skuId,
    padc: sku.padc,
  });
}

// 修改
async function onUpdateCart(params: any) {
  const res: any = await useUpdateCart(params);
  if (res?.result?.code === 200) {
    onProcessCartData(res?.data, false);
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

// 删除商品
async function onDeleteGoods(goods: any) {
  onRemoveCart({ goodsId: goods.goodsId, padc: goods.padc });
}

// 删除sku
async function onDeleteSku(sku: any, goods: any) {
  onRemoveCart({ skuId: sku.skuId, padc: sku.padc });
}

// 删除
async function onRemoveCart(params: any) {
  const res: any = await useRemoveCart(params);
  if (res?.result?.code === 200) {
    message.success(authStore.i18n("cm_find.deleteSuccessMessage"), {
      duration: 3000,
    });
    onProcessCartData(res?.data, false);
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

async function onOpenSkuDialog(sku: any, goods: any) {
  const res: any = await useGoodsInfo({
    id: goods.goodsId,
    padc: sku.padc,
    deviceType: 1,
  });
  if (res?.result?.code === 200) {
    pageData.currentSku = sku;
    pageData.updatedSku = sku;
    pageData.currentGoods = res?.data;
    await onInitGoodsData();
    pageData.dialogVisible = true;
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

async function onInitGoodsData() {
  pageData.selectedSpec = {};
  pageData.currentSku.specItemList.forEach((spec: any) => {
    pageData.selectedSpec[spec.groupId] = spec.itemId;
  });
}

function onSpecUpdate(specId: string, itemId: string, item: any) {
  if (item.disabled) return;
  pageData.selectedSpec[specId] = itemId;
}
// 更新最后一组规格组的状态 最后一组规格会校验sku的库存以及下架状态 如果没有查到sku 默认下架
function updateSpecStatus() {
  // 将对象转成数组 方便处理
  let selectList = <any>[];
  for (const id in pageData.selectedSpec) {
    selectList.push(pageData.selectedSpec[id]);
  }
  // 这里需要将选中的最后一组的规格给移除掉 再去找匹配的sku
  const lastSpecList =
    pageData.currentGoods.specList[pageData.currentGoods.specList.length - 1];
  lastSpecList.items.forEach((item: any) => {
    if (selectList.includes(item.itemId)) {
      const existingIndex = selectList.indexOf(item.itemId);
      if (existingIndex !== -1) {
        selectList.splice(existingIndex, 1);
      }
    }
  });
  lastSpecList.items.forEach((t: any) => {
    t.disabled = true;
  });
  // 获取所有满足条件的SKU
  const matchingSkus = pageData.currentGoods.skuList.filter((sku: any) => {
    return selectList.every((specItemId: string) =>
      sku.specItems.some(
        (skuSpecItem: any) => skuSpecItem.itemId === specItemId
      )
    );
  });
  // 如果有满足条件的SKU，则更新规格的可选状态
  if (matchingSkus.length > 0) {
    matchingSkus.forEach((sku: any) => {
      let disabled = true;
      // 判断当前选中的sku是否是当前sku 如果是则还是可选的
      if (sku.id == pageData.currentSku?.skuId && sku.stockQty != 0) {
        disabled = false;
      }
      // 如果库存为0 则不可选 如果cartQty选购的数量大于0 则是已加购的规格 也是不可选的
      if (sku.stockQty != 0 && sku.cartQty == 0) {
        disabled = false;
      }
      const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
      lastSpecList.items.forEach((item: any) => {
        if (item.itemId == matchingItemId) {
          item.disabled = disabled;
        }
      });
    });
  } else {
    lastSpecList.items.forEach((item: any) => {
      item.disabled = true;
    });
  }
  onFilterSku();
}

// 只有一个规格时 判断当前规格的sku有没有库存以及上下架
function updateOneSpecStatus() {
  const onlyOneSpecItems = pageData.currentGoods?.specList[0].items;
  onlyOneSpecItems.forEach((t: any) => {
    t.disabled = true;
    const matchingSkus = pageData.currentGoods?.skuList.filter((sku: any) => {
      return sku.specItems.some(
        (skuSpecItem: any) => skuSpecItem.itemId === t.itemId
      );
    });
    if (matchingSkus.length > 0) {
      matchingSkus.forEach((sku: any) => {
        let disabled = true;
        // 判断当前选中的sku是否是当前sku 如果是则还是可选的
        if (sku.id == pageData.currentSku?.skuId && sku.stockQty != 0) {
          disabled = false;
        }
        if (sku.stockQty != 0 && sku.cartQty == 0) {
          disabled = false;
        }
        const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
        onlyOneSpecItems.forEach((item: any) => {
          if (item.itemId == matchingItemId) {
            item.disabled = disabled;
          }
        });
      });
    } else {
      onlyOneSpecItems.forEach((item: any) => {
        item.disabled = true;
      });
    }
  });
  onFilterSku();
}

// 筛选出sku
function onFilterSku() {
  const lastSpecList =
    pageData.currentGoods.specList[pageData.currentGoods.specList.length - 1];
  // 选中的sku是下架或者没有库存的情况下 将最后一组规格里选中规格的取消
  for (const id in pageData.selectedSpec) {
    lastSpecList.items.forEach((item: any) => {
      if (item.itemId == pageData.selectedSpec[id] && item.disabled) {
        delete pageData.selectedSpec[id];
      }
    });
  }
  const skus = pageData.currentGoods.skuList.filter((sku: any) => {
    return (
      Object.entries(pageData.selectedSpec).every(([specId, itemId]) => {
        const matchingSpec = sku.specItems.find(
          (spec: any) => spec.specId === specId
        );
        if (!matchingSpec || matchingSpec.itemId !== itemId) {
          return false;
        }
        return true;
      }) && Object.keys(pageData.selectedSpec).length === sku.specItems.length
    );
  });
  pageData.updatedSku = skus.length > 0 ? skus[0] : null;
}

async function onConfirm() {
  const params = {
    selected: true,
    updatedSkuId: pageData.updatedSku.id,
    skuId: pageData.currentSku.skuId,
    goodsId: pageData.currentGoods.id,
    padc: pageData.currentSku.padc,
  };
  const res: any = await useUpdateCart(params);
  if (res?.result?.code === 200) {
    pageData.dialogVisible = false;
    onProcessCartData(res?.data, false);
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

function onCancel() {
  pageData.dialogVisible = false;
}

function onGoHome() {
  window.location.href = "/h5";
}

let tabsOriginalTop = 0;
const handleScroll = () => {
  if (!tabsRef.value || !pageData.showTabs) return;
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  if (tabsOriginalTop === 0) {
    tabsOriginalTop = tabsRef.value.offsetTop;
  }
  pageData.isTabsSticky = scrollTop >= tabsOriginalTop;
};

onMounted(() => {
  window.addEventListener("scroll", handleScroll);
  nextTick(() => {
    if (tabsRef.value) {
      tabsOriginalTop = tabsRef.value.offsetTop;
    }
  });
  document.addEventListener("visibilitychange", handleVisibilityChange);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", handleScroll);
  document.removeEventListener("visibilitychange", handleVisibilityChange);
});

function handleVisibilityChange() {
  if (!document.hidden) {
    const fromCartToOrderDetail = sessionStorage.getItem(
      "fromCartToOrderDetail"
    );
    if (fromCartToOrderDetail === "true") {
      sessionStorage.removeItem("fromCartToOrderDetail");
      onGetCartByTab("refresh");
    }
  }
}
</script>
<style scoped lang="scss">
.mobile-container {
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
  padding-bottom: 3.8rem;
}

:deep(.n-checkbox.n-checkbox--checked .n-checkbox-box) {
  background: #e50113;
}
:deep(.n-checkbox__label) {
  width: 100%;
  padding-left: 0.12rem;
  padding-right: 0.12rem;
}
:deep(.divider .n-divider) {
  margin: 0;
}
.divider.n-divider:not(.n-divider--vertical) {
  margin: 0;
}
:deep(.divider .n-divider__line.n-divider__line--left) {
  display: none;
}
.spec-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 0.8rem;
  margin: 0.13rem 0.32rem 0.13rem 0;
  padding: 0 0.35rem;
  min-width: 0.8rem;
  cursor: pointer;
  color: #797979;
  border-radius: 0.1rem;
  border: 0.02rem solid #d7d7d7;
}
.disabled-btn {
  background: #d7d7d7;
  cursor: not-allowed;
  border: none;
}
.current-btn {
  color: #e50113;
  cursor: pointer;
  border: none;
  background: rgba(229, 1, 19, 0.18);
  .btn-tick {
    opacity: 1;
  }
}
.btn-tick {
  position: absolute;
  right: -0.2rem;
  bottom: 0;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.btn-tick::before {
  content: "";
  position: absolute;
  top: 0.1rem;
  right: 0.04rem;
  bottom: 0.02rem;
  left: 0.02rem;
  background-color: #e50113;
  z-index: -1;
  border-radius: 0.16rem 0 0.1rem 0;
}
:deep(.n-drawer-header) {
  padding-top: 0 !important;
  padding-left: 0.2rem !important;
  padding-right: 0.2rem !important;
  border: none !important;
}
:deep(.n-drawer-footer) {
  border: none !important;
}
:deep(.n-drawer-body-content-wrapper) {
  padding: 0 0.2rem !important;
}
:deep(.n-card__content) {
  color: #000;
  padding: 0.48rem 0 !important;
  text-align: center;
}

.border-btn {
  border: 0.02rem solid #c7c7c7;
  &:hover {
    background: #e50113;
    color: #fff;
    border: none;
  }
}
.loading-overlay {
  position: fixed;
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
}
:deep(.country-delivery) {
  display: none;
}
:deep(.country-code) {
  font-size: 0.28rem;
  line-height: 0.28rem;
}
</style>
