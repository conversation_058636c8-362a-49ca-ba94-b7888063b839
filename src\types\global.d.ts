// 全局类型声明文件

declare global {
  interface Window {
    MyStat?: {
      addPageEvent: (event: string, description: string, callback?: () => void) => void;
      getPageSPM: (event: Event) => string;
      getVisitCode: () => string;
    };
    YT?: {
      PlayerState: {
        PLAYING: number;
        ENDED: number;
      };
    };
    siteData?: {
      siteInfo: {
        id: string;
        code: string;
        name: string;
        logo: string;
      };
      siteList: Array<{
        id: string;
        code: string;
        name: string;
        logo: string;
      }>;
    };
  }
}

export {};
