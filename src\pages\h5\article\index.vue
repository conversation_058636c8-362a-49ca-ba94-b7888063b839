<template>
  <div class="article-wrapper">
    <mobile-search-bar></mobile-search-bar>
    <div
      data-spm-box="article-inner-link"
      v-if="pageData.articleDetail?.content"
      v-html="pageData.articleDetail?.content"
      class="whitespace-pre-wrap px-[0.32rem]"
    ></div>
    <mobile-page-footer></mobile-page-footer>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

const route = useRoute();
const authStore = useAuthStore();
const pageData = reactive<any>({
  articleDetail: "",
});

onGetArticle();
async function onGetArticle() {
  const res: any = await useArticleDetail({
    id: route.query.id,
    title: route.query.title,
    articleCode: route.query.code,
  });
  if (res?.result?.code === 200) {
    pageData.articleDetail = res?.data;
  }
}
</script>
<style scoped lang="scss">
.article-wrapper {
  height: auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 100vh;
}
</style>
