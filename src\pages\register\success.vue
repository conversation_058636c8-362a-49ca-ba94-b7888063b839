<template>
  <div class="!bg-[#f5f3f3]">
    <div class="wrapper">
      <div class="bg-white">
        <search-card></search-card>
      </div>
      <div class="page-content cwidth mx-auto text-[#555]">
        <div class="text-[32px] font-medium mb-5 text-[#333]">
          {{ authStore.i18n("cm_common.thankYouRegister") }}
        </div>
        <n-space vertical :style="{ gap: '12px 0' }" class="text-[16px]">
          <div>
            {{ authStore.i18n("cm_common.regSuccessStart") }}
          </div>
          <div>
            {{ authStore.i18n("cm_common.checkQuickGuide") }}
            <a
              target="_blank"
              href="/article/quick-guide"
              class="text-[#e50113] hover:underline"
              >{{ authStore.i18n("cm_news.quickGuide") }}</a
            >.
          </div>
          <div>
            <span v-if="pageData.couponList?.length">
              {{ authStore.i18n("cm_common.activationReward") }}:
              <span
                v-for="(coupon, index) in pageData.couponList"
                :key="index"
                class="text-[15px] font-medium"
              >
                {{ coupon?.couponName }}
                <span>x{{ coupon?.count }}</span>
                <span
                  class="text-[#555]"
                  v-if="index < pageData.couponList.length - 1"
                  >,
                </span> </span
              >.
            </span>
            <span v-else>{{ authStore.i18n("cm_nota.emailActivate") }}</span>
            <span>
              {{ authStore.i18n("cm_common.activationReward") }}
            </span>
            {{ authStore.i18n("cm_common.activationEmail") }}
            <span class="text-[15px] font-medium break-all">
              {{ userInfo?.username }}
            </span>
            {{ authStore.i18n("cm_common.activationValidTime") }}
            <span class="text-[15px] font-medium">
              {{ pageData.expireHour }}
              <span v-if="pageData?.expireHour > 1">{{
                authStore.i18n("cm_common.activationTimeUnits")
              }}</span>
              <span v-if="pageData?.expireHour === 1">{{
                authStore.i18n("cm_common.activationTimeUnit")
              }}</span></span
            >.
          </div>
          <div>
            <n-button
              color="#E50113"
              text-color="#fff"
              @click="onQueryVerifyMailResult('verifyMail')"
              class="rounded-[4px] w-[120px] h-[38px] text-[16px] mr-[22px] mt-2"
            >
              <div>{{ authStore.i18n("cm_common_emailActivate") }}</div>
            </n-button>
            {{ authStore.i18n("cm_common.or") }}
            <a
              href="/"
              class="text-[16px] hover:underline hover:text-[#E50113]"
              >{{ authStore.i18n("cm_common.skipToMyChilatshop") }}</a
            >
          </div>
        </n-space>
        <div class="border-t-1 mt-[42px]">
          <div class="mt-[20px] mb-[12px] text-[18px] font-medium">
            {{ authStore.i18n("cm_common.verificationEmail") }}
          </div>
          <ul>
            <n-space vertical :style="{ gap: '6px 0' }">
              <li>
                {{ authStore.i18n("cm_common.spamCheck") }}
              </li>
              <li>{{ authStore.i18n("cm_common.deliveryDelay") }}</li>
              <li>
                {{ authStore.i18n("cm_common.verificationDelay") }}

                <span
                  class="text-[#636ded] cursor-pointer"
                  @click="resendVerification"
                >
                  {{ authStore.i18n("cm_common.resendVerification") }}
                </span>
              </li>
            </n-space>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const userInfo = ref<any>({});
userInfo.value = config.public.userInfo as object;

const pageData = reactive({
  expireHour: 0,
  couponList: <any>[],
});

useHead({
  title: authStore.i18n("cm_common.chilatTitle"),
});

onQueryVerifyMailResult();
// 查询邮箱是否已验证, 以及验证后可以得到的优惠券
async function onQueryVerifyMailResult(type?: any) {
  const res: any = await useQueryVerifyMailResult({
    email: userInfo?.value?.username,
    isNeedCoupon: true,
    verifyMailScene: "REGISTER",
  });
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    if (res?.data?.isMailVerified) {
      if (type === "verifyMail") {
        window.location.replace("/user/coupon");
      } else {
        navigateTo("/user/coupon");
      }
    } else if (type === "verifyMail") {
      navigateToEmail();
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}

// 发送验证邮箱的邮件
async function resendVerification() {
  const res: any = await useSendVerifyMail({
    verifyMailScene: "REGISTER",
  });
  if (res?.result?.code === 200) {
    if (res?.data?.isMailVerified) {
      window.location.replace("/user/coupon");
    } else {
      window?.MyStat?.addPageEvent(
        "passport_resend_active_mail",
        `重发邮箱验证邮件：成功`
      ); // 埋点
      showToast(authStore.i18n("cm_common.resendSuccess"));
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_resend_active_mail",
      `重发邮箱验证邮件：${res?.result?.message}`
    ); // 埋点
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  min-height: 70vh;
  width: 100%;
}
.page-content {
  background: url("@/assets/icons/submitSuccess.svg") no-repeat 40px 50px;
  background-size: 50px 50px;
  box-sizing: border-box;
  margin: 0 auto;
  padding: 50px 50px 30px 110px;
  position: relative;
  background-color: #fff;
  min-height: 60vh;
  margin-top: 20px;
}
ul {
  list-style-type: disc;
  padding-left: 16px;
}
</style>
