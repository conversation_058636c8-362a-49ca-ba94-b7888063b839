<template>
  <div class="w-[1280px] container">
    <search-step-card :currentStep="2"></search-step-card>
    <div class="pt-[50px] pb-[160px]">
      <div class="w-[506px] mx-auto text-center">
        <img
          loading="lazy"
          alt="paySuccess"
          src="@/assets/icons/order/paySuccess.svg"
          class="w-[64px] h-[64px] mx-auto mb-[24px]"
        />
        <div class="font-medium text-[26px] leading-[26px] text-[#333]">
          {{ authStore.i18n("cm_search.sentSuccess") }}
        </div>
        <div class="text-[20px] leading-[20px] mt-[16px]">
          {{ authStore.i18n("cm_find_submitSuccessTip") }}
          <span class="font-medium text-[#25D366]">WhatsApp!</span>
        </div>
        <div class="text-[18px] leading-[18px] mt-[10px]">
          {{ authStore.i18n("cm_search.successEmail") }}
          <span class="font-medium text-[#e50113]">{{ pageData.email }}</span>
        </div>
        <div class="text-[16px] leading-[20px] mt-[18px] text-[#939393]">
          <span>{{ authStore.i18n("cm_search.orderShipped") }}</span>
          ({{ pageData.whatsapp }})
          <span>{{ authStore.i18n("cm_search.shippingQuote") }}</span>
        </div>
      </div>

      <div
        class="w-[506px] mx-auto mt-[30px] pt-[24px] border-t-1 border-[#BBB]"
        v-if="!pageData.firstSubmit"
      >
        <a href="/">
          <n-button
            block
            color="#e50113"
            data-spm-box="pay-result-go-detail"
            class="w-[274px] h-[42px] rounded-[200px] custom-btn"
          >
            <span class="text-[22px] leading-[22px]">{{
              authStore.i18n("cm_search.goToHome")
            }}</span>
          </n-button>
        </a>
      </div>
      <div
        v-else
        class="w-[568px] mx-auto mt-[30px] pt-[24px] border-t-1 border-[#BBB]"
      >
        <template
          v-if="!pageData.isMailVerified && pageData.couponList?.length"
        >
          <div
            class="w-full px-[40px] pt-[34px] pb-[26px] rounded-[20px] bg-[#fafafa]"
          >
            <div class="text-[18px] leading-[22px] text-[#4D4D4D]">
              {{ authStore.i18n("cm_common.activationReward") }}:
            </div>
            <coupon-card :couponList="pageData.couponList"></coupon-card>
          </div>
          <div
            class="text-[16px] leading-[20px] text-[#919191] text-center my-[24px] px-[40px]"
          >
            {{ authStore.i18n("cm_common.activationEmail") }}

            {{ userInfo?.username }} ,
            {{ authStore.i18n("cm_common.activationValidTime") }}
            <span class="text-[#e50113]">
              {{ pageData.expireHour }}
              <span v-if="pageData?.expireHour > 1">{{
                authStore.i18n("cm_common.activationTimeUnits")
              }}</span>
              <span v-if="pageData?.expireHour === 1">{{
                authStore.i18n("cm_common.activationTimeUnit")
              }}</span></span
            >.
          </div>
          <n-button
            block
            color="#E50113"
            text-color="#fff"
            @click="onQueryVerifyMailResult('verifyMail')"
            class="rounded-[200px] h-[42px] text-[22px] custom-btn"
          >
            <div>{{ authStore.i18n("cm_common_emailActivate") }}</div>
          </n-button>
          <div class="border-t-1 mt-[40px] pt-[30px]">
            <div class="text-[22px] leading-[22px] text-[#e50113] font-medium">
              {{ authStore.i18n("cm_common.verificationEmail") }}
            </div>
            <ul class="mt-[18px] text-[16px] leading-[24px]">
              <n-space vertical :style="{ gap: '8px 0' }">
                <li>
                  {{ authStore.i18n("cm_common.spamCheck") }}
                </li>
                <li>{{ authStore.i18n("cm_common.deliveryDelay") }}</li>
                <li>
                  {{ authStore.i18n("cm_common.verificationDelay") }}

                  <span
                    class="text-[#4290F7] cursor-pointer text-[18px] font-medium underline"
                    @click="resendVerification"
                  >
                    {{ authStore.i18n("cm_common.resendVerification") }}
                  </span>
                </li>
              </n-space>
            </ul>
          </div>
        </template>
        <template v-if="pageData.isMailVerified && pageData.couponList?.length">
          <div
            class="w-full px-[40px] pt-[34px] pb-[26px] rounded-[20px] bg-[#fafafa]"
          >
            <div class="text-[18px] leading-[22px] text-[#4D4D4D]">
              {{ authStore.i18n("cm_common.rewardInquiry") }}:
            </div>
            <coupon-card :couponList="pageData.couponList"></coupon-card>
          </div>
          <div
            class="mt-[12px] py-[12px] px-[30px] bg-[#FAFAFA] text-[20px] leading-[28px] text-center rounded-[20px]"
          >
            {{ authStore.i18n("cm_common.issuedToAccount") }}
            <a href="/user/coupon" class="text-[#e50113] font-medium underline">
              <span class="text-[#e50113] hover:underline">{{
                authStore.i18n("cm_common.viewInCoupons")
              }}</span>
            </a>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CouponCard from "./components/CouponCard.vue";
import SearchStepCard from "./components/SearchStepCard.vue";
import { useAuthStore } from "@/stores/authStore";

const route = useRoute();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const userInfo = ref<any>({});
userInfo.value = config.public.userInfo as object;

const pageData = reactive<any>({
  email: route.query.email || "",
  whatsapp: route?.query?.whatsapp || "",
  firstSubmit: route.query?.firstSubmit === "true" ? true : false,
  isMailVerified: false, // 邮箱是否已验证
  expireHour: 0,
  couponList: <any>[],
});

if (!!window?.gtag) {
  window?.gtag("event", "conversion", {
    send_to: "AW-16652189587/_rw6COCumMkZEJP_sIQ-",
  });
}

if (pageData.firstSubmit) {
  onQueryVerifyMailResult();
}

// 查询邮箱是否已验证
async function onQueryVerifyMailResult(type?: any) {
  const res: any = await useQueryVerifyMailResult({
    email: userInfo?.value?.username,
    isNeedCoupon: true,
    verifyMailScene: "FIRST_GOODS_LOOKING",
  });
  if (res?.result?.code === 200) {
    if (type === "verifyMail") {
      if (res?.data?.isMailVerified) {
        window.location.replace("/user/coupon");
      } else {
        navigateToEmail();
      }
    }
    Object.assign(pageData, res?.data);
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}

// 发送验证邮箱的邮件
async function resendVerification() {
  const res: any = await useSendVerifyMail({
    verifyMailScene: "FIRST_GOODS_LOOKING",
  });
  if (res?.result?.code === 200) {
    if (res?.data?.isMailVerified) {
      window.location.replace("/user/coupon");
    } else {
      window?.MyStat?.addPageEvent(
        "passport_resend_active_mail",
        `重发邮箱验证邮件：成功`
      ); // 埋点
      showToast(authStore.i18n("cm_common.resendSuccess"));
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_resend_active_mail",
      `重发邮箱验证邮件：${res?.result?.message}`
    ); // 埋点
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}
</script>

<style scoped lang="scss">
.container {
  height: auto;
  margin: 0 auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 70vh;
}

ul {
  list-style-type: disc;
  padding-left: 16px;
}
.custom-btn.n-button:hover {
  background: #f20114;
}
</style>
