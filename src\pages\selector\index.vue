<template>
  <page-layout>
    <template #main-content>
      <div class="min-h-[60vh]">
        <div class="flex flex-col gap-[60px] pt-[20px] pb-[200px]">
          <category-carousal
            v-for="item in pageData.selectorList"
            :key="item.selectorId"
            :selector="item"
          ></category-carousal>
        </div>
      </div>
    </template>
  </page-layout>
</template>
<script setup lang="ts">
import CategoryCarousal from "@/pages/components/CategoryCarousal.vue";

const route = useRoute();
const pageData = reactive({
  selectorList: <any>[],
});

await onGetPromotionGoodsIndexByCode();
async function onGetPromotionGoodsIndexByCode() {
  const res: any = await useGetPromotionGoodsIndexByCode({
    code: route.query.code,
  });

  if (res?.result?.code === 200) {
    res.data?.selectorList?.forEach((item: any) => {
      item.goodsList = useChunk(item.goodsList, 6);
      item.selectorType = res.data?.selectorType;
      item.spmCode = "activity-goods-list";
      item.activityId = res.data?.id;
    });
    pageData.selectorList = res.data?.selectorList;
  }
}
</script>

<style scoped lang="scss"></style>
