<template>
  <div class="mobile-container bg-[#f6f6f6]">
    <!-- 头部信息 -->
    <mobile-search-bar></mobile-search-bar>
    <div class="flex flex-col pt-[0.4rem] pb-[1.2rem]">
      <category-card
        v-for="(item, index) in pageData.selectorList"
        :key="item.selectorId"
        :cateInfo="item"
        :cateColor="cateColorArr[index + 1]"
      ></category-card>
    </div>
    <mobile-page-footer></mobile-page-footer>
  </div>
</template>
<script setup lang="ts">
import CategoryCard from "@/pages/h5/components/CategoryCard.vue";
import { cateColorArr } from "@/utils/constant";

const route = useRoute();
const pageData = reactive({
  selectorList: <any>[],
});

await onGetPromotionGoodsIndexByCode();
async function onGetPromotionGoodsIndexByCode() {
  const res: any = await useGetPromotionGoodsIndexByCode({
    code: route.query.code,
  });

  if (res?.result?.code === 200) {
    res.data?.selectorList?.forEach((item: any) => {
      item.selectorType = res.data?.selectorType;
      item.spmCode = "activity-goods-list";
      item.activityId = res.data?.id;
    });
    pageData.selectorList = res.data?.selectorList;
  }
}
</script>

<style scoped lang="scss"></style>
