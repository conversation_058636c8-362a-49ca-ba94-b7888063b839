<template>
  <div>
    <!-- 头部信息 -->
    <mobile-search-bar :showCart="true"></mobile-search-bar>
    <mobile-step-card :currentStep="2"></mobile-step-card>
    <div
      class="py-[0.36rem] border-b-1 border-[#F2F2F2] text-center text-[0.36rem] leading-[0.36rem] font-medium"
    >
      {{ authStore.i18n("cm_common.step3") }}
    </div>
    <!-- 信息提示 -->
    <div class="pt-[0.6rem] px-[0.48rem] pb-[2.4rem]">
      <div class="text-center">
        <img
          loading="lazy"
          alt="paySuccess"
          src="@/assets/icons/order/paySuccess.svg"
          class="w-[1.04rem] h-[1.04rem] mx-auto mb-[0.36rem]"
          referrerpolicy="no-referrer"
        />
        <div class="font-medium text-[0.52rem] leading-[0.52rem] text-[#333]">
          {{ authStore.i18n("cm_search.sentSuccess") }}
        </div>
        <div class="text-[0.32rem] leading-[0.44rem] mt-[0.36rem]">
          {{ authStore.i18n("cm_find_submitSuccessTip") }}
          <span class="font-medium text-[#25D366]">WhatsApp!</span>
        </div>
        <div class="text-[0.28rem] leading-[0.4rem] mt-[0.08rem]">
          {{ authStore.i18n("cm_search.successEmail") }}
          <span class="font-medium text-[#e50113]">{{ pageData.email }}</span>
        </div>
        <div class="text-[0.28rem] leading-[0.4rem] mt-[0.2rem] text-[#939393]">
          <span>{{ authStore.i18n("cm_search.orderShipped") }}</span>
          ({{ pageData.whatsapp }})
          <span>{{ authStore.i18n("cm_search.shippingQuote") }}</span>
        </div>
      </div>
      <div
        v-if="!pageData.firstSubmit"
        class="mt-[0.52rem] pt-[0.36rem] border-t-1 border-[#BBB]"
      >
        <a href="/h5">
          <n-button
            block
            color="#e50113"
            data-spm-box="pay-result-go-detail"
            class="h-[0.72rem] rounded-[4rem]"
          >
            <span class="text-[0.32rem] leading-[0.32rem] font-medium">{{
              authStore.i18n("cm_search.goToHome")
            }}</span>
          </n-button>
        </a>
      </div>
      <div v-else class="mt-[0.52rem] pt-[0.4rem] border-t-1 border-[#BBB]">
        <template
          v-if="!pageData.isMailVerified && pageData.couponList?.length"
        >
          <div
            class="w-full px-[0.4rem] pt-[0.52rem] pb-[0.32rem] rounded-[0.4rem] bg-[#fafafa]"
          >
            <div class="text-[0.32rem] leading-[0.4rem] text-[#4D4D4D]">
              {{ authStore.i18n("cm_common.activationReward") }}:
            </div>
            <coupon-card :couponList="pageData.couponList"></coupon-card>
          </div>
          <div
            class="text-[0.28rem] leading-[0.4rem] text-[#919191] text-center my-[0.4rem]"
          >
            {{ authStore.i18n("cm_common.activationEmail") }}

            {{ userInfo?.username }} ,
            {{ authStore.i18n("cm_common.activationValidTime") }}
            <span class="text-[#e50113]">
              {{ pageData.expireHour }}
              <span v-if="pageData?.expireHour > 1">{{
                authStore.i18n("cm_common.activationTimeUnits")
              }}</span>
              <span v-if="pageData?.expireHour === 1">{{
                authStore.i18n("cm_common.activationTimeUnit")
              }}</span></span
            >.
          </div>
          <n-button
            block
            color="#E50113"
            text-color="#fff"
            @click="onQueryVerifyMailResult('verifyMail')"
            class="rounded-[4rem] h-[0.72rem] text-[0.32rem] custom-btn"
          >
            <div>{{ authStore.i18n("cm_common_emailActivate") }}</div>
          </n-button>
          <div class="border-t-1 mt-[0.72rem] pt-[0.52rem]">
            <div
              class="text-[0.44rem] leading-[0.52rem] text-[#e50113] font-medium"
            >
              {{ authStore.i18n("cm_common.verificationEmail") }}
            </div>
            <ul class="mt-[0.24rem] text-[0.28rem] leading-[0.4rem]">
              <n-space vertical :style="{ gap: '0.12rem 0' }">
                <li>
                  {{ authStore.i18n("cm_common.spamCheck") }}
                </li>
                <li>{{ authStore.i18n("cm_common.deliveryDelay") }}</li>
                <li>
                  {{ authStore.i18n("cm_common.verificationDelay") }}

                  <span
                    class="text-[#4290F7] cursor-pointer"
                    @click="resendVerification"
                  >
                    {{ authStore.i18n("cm_common.resendVerification") }}
                  </span>
                </li>
              </n-space>
            </ul>
          </div>
        </template>
        <template v-if="pageData.isMailVerified && pageData.couponList?.length">
          <div
            class="w-full px-[0.4rem] pt-[0.52rem] pb-[0.32rem] rounded-[0.4rem] bg-[#fafafa]"
          >
            <div class="text-[0.32rem] leading-[0.4rem] text-[#4D4D4D]">
              {{ authStore.i18n("cm_common.rewardInquiry") }}:
            </div>
            <coupon-card :couponList="pageData.couponList"></coupon-card>
          </div>
          <div
            class="mt-[0.16rem] py-[0.24rem] px-[0.4rem] bg-[#FAFAFA] text-[0.36rem] leading-[0.48rem] text-center rounded-[0.4rem]"
          >
            {{ authStore.i18n("cm_common.issuedToAccount") }}<br />
            <a
              href="/h5/user/coupon"
              class="text-[#e50113] font-medium underline"
            >
              <span class="text-[#e50113] hover:underline">{{
                authStore.i18n("cm_common.viewInCoupons")
              }}</span>
            </a>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import CouponCard from "./components/CouponCard.vue";
import MobileStepCard from "./components/MobileStepCard.vue";
const route = useRoute();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const userInfo = ref<any>({});
userInfo.value = config.public.userInfo as object;

const pageData = reactive({
  email: route.query.email || "",
  whatsapp: route?.query?.whatsapp || "",
  firstSubmit: route.query?.firstSubmit === "true" ? true : false,
  isMailVerified: false, // 邮箱是否已验证
  expireHour: 0,
  couponList: <any>[],
});

function onGoHome() {
  window.location.href = `/h5`;
}

if (!!window?.gtag) {
  window?.gtag("event", "conversion", {
    send_to: "AW-16652189587/_rw6COCumMkZEJP_sIQ-",
  });
}

if (pageData.firstSubmit) {
  onQueryVerifyMailResult();
}

// 查询邮箱是否已验证
async function onQueryVerifyMailResult(type?: any) {
  const res: any = await useQueryVerifyMailResult({
    email: userInfo?.value?.username,
    isNeedCoupon: true,
    verifyMailScene: "FIRST_GOODS_LOOKING",
  });
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    if (type === "verifyMail") {
      if (res?.data?.isMailVerified) {
        window.location.replace("/h5/user/coupon");
      } else {
        navigateToEmail();
      }
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}

// 发送验证邮箱的邮件
async function resendVerification() {
  const res: any = await useSendVerifyMail({
    verifyMailScene: "FIRST_GOODS_LOOKING",
  });
  if (res?.result?.code === 200) {
    if (res?.data?.isMailVerified) {
      window.location.replace("/h5/user/coupon");
    } else {
      window?.MyStat?.addPageEvent(
        "passport_resend_active_mail",
        `重发邮箱验证邮件：成功`
      ); // 埋点
      showToast(authStore.i18n("cm_common.resendSuccess"));
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_resend_active_mail",
      `重发邮箱验证邮件：${res?.result?.message}`
    ); // 埋点
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}
</script>
<style scoped>
ul {
  list-style-type: disc;
  padding-left: 0.32rem;
}
</style>
