<template>
  <div
    class="py-[30px] px-[20px] bg-white rounded-bl-[12px] rounded-br-[12px] rounded-tr-[12px]"
  >
    <div class="flex gap-[10px]">
      <!-- 左侧商品列表 -->
      <div class="flex-1">
        <div
          class="text-[#1EA62A] text-[28px] leading-[28px] font-medium"
          :class="props.showTabs ? 'text-[#1EA62A]' : 'text-[#333]'"
        >
          {{ authStore.i18n("cm_find.directOrderList") }}
        </div>

        <div class="mt-[30px]">
          <div class="text-[16px] leading-[16px] font-medium mb-[20px]">
            {{ authStore.i18n("cm_addr.shippingAddress") }}
          </div>
          <!-- 有地址时显示选中的地址 -->
          <div
            v-if="!isEmptyObject(pageData?.orderAddress)"
            class="border-1 border-[#D9D9D9] flex items-center rounded-[12px] pt-[12px] pr-[14px] pb-[14px] pl-[12px]"
          >
            <icon-card
              name="mdi:address-marker"
              size="30"
              color="#e50113"
              class="mr-2"
            >
            </icon-card>
            <div class="flex-1">
              <div class="mb-1.5">
                <span class="font-medium mr-[12px]">{{
                  pageData?.orderAddress?.contactName
                }}</span>
                <span>{{ onFormatPhone(pageData?.orderAddress) }}</span>
              </div>
              <div>
                {{ pageData?.orderAddress?.fullAddress }}
              </div>
            </div>
            <img
              alt="edit"
              @click="onShowSelectAddress"
              class="w-[18Ppx] ml-[32px] cursor-pointer"
              src="@/assets/icons/find/edit.svg"
            />
          </div>

          <!-- 没有地址时显示地址填写提示 -->
          <div v-else>
            <AddressForm
              ref="inlineAddressFormRef"
              :country-list="pageData.countryList"
              :country-regexes="pageData.countryRegexes"
              @onUpdateListUserAddress="onUpdateListUserAddress"
              @onCloseAddAddr="onCloseAddAddr"
              type="inline"
            />
          </div>
        </div>
        <div v-if="cartData?.goodsList?.length" class="mt-[34px] pr-[10px]">
          <!-- 全选 -->
          <n-checkbox
            size="large"
            v-model:checked="selectAll"
            class="text-[16px] leading-[16px] font-medium mb-[28px] w-full"
            @update:checked="onAllSelection"
          >
            <n-divider class="divider" title-placement="left">
              {{ authStore.i18n("cm_find.selectAllItems") }}
              (<span>{{ cartData.stat?.goodsCount || 0 }}</span
              >)
            </n-divider>
          </n-checkbox>

          <!-- 商品列表 -->
          <div
            v-for="(goods, index) in cartData.goodsList"
            :key="goods.goodsId"
            class="mb-4"
          >
            <div class="flex">
              <n-checkbox
                class="mr-2"
                v-model:checked="goods.selected"
                @update:checked="
                  (value) => $emit('onGoodsSelection', value, goods)
                "
              />
              <goods-card
                :goods="goods"
                class="flex-1 mr-10"
                :spmIndex="index"
                spmCode="cart-goods-list"
              />
              <icon-card
                name="uil:trash-alt"
                color="#797979"
                size="28"
                @click="$emit('onDeleteGoods', goods)"
              />
            </div>

            <!-- SKU列表 -->
            <div
              v-for="sku in goods.skuList"
              :key="sku.skuId"
              class="sku-checkbox my-3 ml-10"
            >
              <div class="flex items-center">
                <n-checkbox
                  v-model:checked="sku.selected"
                  @update:checked="
                    (value) => $emit('onSkuSelection', value, sku, goods)
                  "
                  class="flex-1"
                />
                <sku-card
                  :sku="sku"
                  :goods="goods"
                  @onCartQtyUpdate="
                    (value) => $emit('onCartQtyUpdate', value, sku, goods)
                  "
                  :step="sku.minIncreaseQuantity"
                >
                  <icon-card
                    name="iconamoon:arrow-right-2"
                    color="#797979"
                    size="26"
                    class="ml-4 cursor-pointer"
                    @click.stop="$emit('onOpenSkuDialog', sku, goods)"
                  />
                </sku-card>
                <icon-card
                  name="uil:trash-alt"
                  color="#797979"
                  size="28"
                  @click="$emit('onDeleteSku', sku, goods)"
                />
              </div>
            </div>
            <n-divider v-if="index !== cartData.goodsList.length - 1" />
          </div>
        </div>

        <n-empty
          v-else
          :description="authStore.i18n('cm_order.noData')"
          class="mt-24"
        >
          <template #extra>
            <n-button
              size="small"
              color="#E50113"
              text-color="#fff"
              @click="onGoHome"
            >
              {{ authStore.i18n("cm_find.goHome") }}
            </n-button>
          </template>
        </n-empty>
      </div>

      <!-- 右侧费用明细 -->
      <div class="w-[400px]">
        <div class="bg-white rounded-lg sticky top-5 right-0">
          <n-affix :trigger-top="20" class="h-fit" id="submit-affix">
            <div
              class="w-[400px] flex flex-col gap-[20px] px-[20px] py-[24px] text-[16px] leading-[16px] text-[#1A1A1A]"
            >
              <div
                class="text-[18px] leading-[18px] font-medium mb-[4px]"
                :class="props.showTabs ? 'text-[#1EA62A]' : 'text-[#333]'"
              >
                {{ authStore.i18n("cm_find.orderSummary") }}
              </div>

              <country-select
                mode="popover"
                @save="onSaveCountry"
                spm="select_site_from_cart"
                class="find-country-select"
              />

              <div class="flex justify-between">
                <span>{{ authStore.i18n("cm_find_quantityOfUnits") }}</span>
                <span>
                  <span class="font-medium">{{
                    cartData.stat?.selectSkuTotalQuantity || 0
                  }}</span>
                  {{
                    cartData.stat?.selectSkuTotalQuantity > 1
                      ? authStore.i18n("cm_find_totalSkuUnits")
                      : authStore.i18n("cm_find_totalSkuUnit")
                  }}
                </span>
              </div>
              <div class="flex flex-col gap-[16px]">
                <div class="flex justify-between">
                  <span>{{ authStore.i18n("cm_find_itemsCost") }}:</span>
                  <span class="font-medium">
                    {{ setUnit(cartData.stat?.selectGoodsSalePrice || 0) }}
                  </span>
                </div>

                <div class="flex justify-between">
                  <span>{{ authStore.i18n("cm_news.commission") }}:</span>
                  <span class="font-medium">
                    {{ setUnit(cartData.stat?.selectCommission || 0) }}
                  </span>
                </div>

                <div class="flex justify-between">
                  <span>{{ authStore.i18n("cm_find.finalShipping") }}:</span>
                  <span class="font-medium">
                    {{ setUnit(cartData.stat?.selectRouteFee || 0) }}
                  </span>
                </div>
              </div>

              <div class="flex justify-between text-[18px] leading-[18px]">
                <span>{{ authStore.i18n("cm_find.totalPayment") }}:</span>
                <span class="font-medium text-[#e50113]">
                  {{ setUnit(cartData.stat?.selectTotalActualPrice) }}
                </span>
              </div>

              <n-button
                size="large"
                color="#E50113"
                text-color="#fff"
                @click="onGoDirectPayment($event)"
                data-spm-box="cart-to-checkout"
                class="rounded-[500px] w-full h-[50px] mt-[16px]"
              >
                <span class="text-[18px] leading-[18px]">
                  {{ authStore.i18n("cm_find.confirmOrder") }}
                </span>
              </n-button>
            </div>
          </n-affix>
        </div>
      </div>
    </div>
  </div>
  <!-- 选择收货地址模态框 -->
  <n-modal
    v-model:show="pageData.showSelectAddressModal"
    :show-icon="false"
    :mask-closable="false"
    aria-modal="true"
  >
    <n-card
      style="width: 800px; max-height: 80vh; overflow: auto"
      :bordered="false"
      role="dialog"
      aria-modal="true"
      closable
      @close="onCloseSelectAddress"
    >
      <template #header>
        <div class="text-[18px] font-medium">
          {{ authStore.i18n("cm_addr.selectShippingAddress") }}
        </div>
      </template>

      <div class="mb-4">
        <div
          @click="onOpenAddAddr()"
          class="inline-block px-[16px] py-[6px] border-1 border-[#A6A6A6] rounded-[500px] hover:text-[#fff] hover:bg-[#E50113] hover:border-[#E50113] transition-all duration-200 cursor-pointer"
        >
          + {{ authStore.i18n("cm_addr.createNewAddress") }}
        </div>
      </div>

      <!-- 地址列表 -->
      <div class="space-y-3 overflow-y-auto max-h-[540px]">
        <div
          v-for="address in pageData.addressList"
          :key="address.id"
          class="border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-[#e50113] transition-colors"
          @click="onSelectAddress(address)"
        >
          <div class="flex items-start justify-between">
            <div class="flex items-start flex-1">
              <!-- 选择按钮 -->
              <n-radio
                :checked="pageData.selectedAddress?.id === address.id"
                @click.stop="onSelectAddress(address)"
                class="mr-3 mt-1"
              />

              <!-- 地址信息 -->
              <div class="flex-1">
                <div class="items-baseline mb-2">
                  <span class="font-medium text-[16px] mr-3">{{
                    address.contactName
                  }}</span>
                  <span class="text-gray-600 whitespace-nowrap">{{
                    onFormatPhone(address)
                  }}</span>
                </div>
                <div class="text-gray-700 text-[14px] leading-relaxed mb-2">
                  {{ address.fullAddress }}
                </div>
                <div
                  v-if="address.isDefault"
                  class="text-[#f0a020] bg-[#f0a02026] inline-block px-[4px]"
                >
                  {{ authStore.i18n("cm_addr.defaultShippingAddress") }}
                </div>
                <div
                  v-else
                  text
                  @click.stop="onAddrToDefault(address)"
                  class="underline"
                >
                  {{ authStore.i18n("cm_addr.setAsDefault") }}
                </div>
              </div>
            </div>

            <div class="flex gap-[16px] items-center">
              <icon-card
                color="#333"
                size="24"
                name="la:edit-solid"
                @click.stop="onOpenEditAddr(address)"
              ></icon-card>
              <!-- <n-popconfirm
                :positive-text="authStore.i18n('cm_addr.confirmBtn')"
                :negative-text="authStore.i18n('cm_addr.cancelBtn')"
                @positive-click="onConfirmDelete(address)"
                placement="top"
                to="body"
                :show-icon="false"
              >
                <template #trigger>
                  <icon-card
                    color="#333"
                    size="18"
                    name="uiw:delete"
                    @click.stop
                  ></icon-card>
                </template>
                {{ authStore.i18n("cm_addr.delAddrTip") }}
              </n-popconfirm> -->
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-end space-x-3 mt-[20px]">
        <n-button round @click="onCloseSelectAddress">{{
          authStore.i18n("cm_addr.cancelAddress")
        }}</n-button>
        <n-button
          round
          type="primary"
          @click="onConfirmSelectAddress"
          :disabled="!pageData.selectedAddress"
        >
          {{ authStore.i18n("cm_addr.sendToThisAddress") }}
        </n-button>
      </div>
    </n-card>
  </n-modal>

  <!-- 地址编辑表单模态框 -->
  <n-modal
    preset="dialog"
    :show-icon="false"
    :style="{
      width: '90%',
      overflow: 'auto',
      maxWidth: '900px',
      padding: '20px 30px',
    }"
    v-model:show="pageData.dialogVisible"
    :on-close="onCloseAddAddr"
    :on-esc="onCloseAddAddr"
    :closable="true"
    :mask-closable="false"
  >
    <div class="text-[18px] leading-[18px] font-medium">
      {{ authStore.i18n("cm_addr.deliveryAddr") }}
    </div>
    <div class="mt-[20px]">
      <AddressForm
        ref="modalAddressFormRef"
        :country-list="pageData.countryList"
        :country-regexes="pageData.countryRegexes"
        @onUpdateListUserAddress="onUpdateListUserAddress"
        @onCloseAddAddr="onCloseAddAddr"
      />
    </div>
  </n-modal>

  <!-- 错误弹窗 -->
  <ErrorModal
    :message="pageData.errorMessage"
    v-model:visible="pageData.errorDialogVisible"
  />
  <SubmitLoadingModal :show="pageData.submitLoading" />
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import GoodsCard from "./GoodsCard.vue";
import SkuCard from "./SkuCard.vue";
import ErrorModal from "./ErrorModal.vue";
import SubmitLoadingModal from "./SubmitLoadingModal.vue";
import AddressForm from "./AddressForm.vue";
import { useMessage } from "naive-ui";

const authStore = useAuthStore();
const config = useRuntimeConfig();
const message = useMessage();
const inlineAddressFormRef = ref<any>(null);
const modalAddressFormRef = ref<any>(null);
const editForm = reactive<any>({});

const props = defineProps({
  cartData: {
    type: Object,
    default: () => {},
  },
  showTabs: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "onAllSelection",
  "onGoodsSelection",
  "onSkuSelection",
  "onCartQtyUpdate",
  "onDeleteGoods",
  "onDeleteSku",
  "onOpenSkuDialog",
  "onGoLoginRegister",
]);

const pageData = reactive<any>({
  errorMessage: "",
  errorDialogVisible: false,
  submitLoading: false,
  addressList: <any>[],
  orderAddress: <any>{},
  showSelectAddressModal: false,
  dialogVisible: false,

  countryList: <any>[],
  provinceList: <any>[],
  cityList: <any>[],
  selectedAddress: <any>{},
  countryRegexes: <any>{},
});

// 计算全选状态
const selectAll = computed(() => {
  return (
    props.cartData?.goodsList?.every((goods: any) => goods.selected) || false
  );
});

onListUserAddress("init");
onGetCountry();

async function onListUserAddress(type?: string) {
  const res: any = await useListUserAddress({});
  if (res?.result?.code === 200) {
    // 只保留秘鲁国家的地址
    const peruAddresses =
      res?.data?.filter(
        (item: any) => item.countryId === config.public.peruCountryId
      ) || [];

    pageData.addressList = peruAddresses;

    // 地址选择逻辑：优先选择默认地址，如果没有默认地址则选择第一条
    if (type === "init" && peruAddresses.length > 0) {
      // 查找默认地址
      const defaultAddress = peruAddresses.find((item: any) => item.isDefault);

      if (defaultAddress) {
        // 有默认地址，选择默认地址
        pageData.orderAddress = defaultAddress;
      } else {
        // 没有默认地址，选择第一条秘鲁地址
        pageData.orderAddress = peruAddresses[0];
      }
    }
  } else if (res?.result?.code === 403) {
    emit("onGoLoginRegister");
  }
}

// 全选操作
function onAllSelection(value: boolean) {
  emit("onAllSelection", value);
}

function onGoHome() {
  window.location.href = "/";
}

// 保存选择的国家
const onSaveCountry = () => {
  // 可以根据需要决定是否重新加载页面
  window.location.reload();
};

// 直接下单处理
async function onGoDirectPayment(event: any) {
  if (isEmptyObject(pageData?.orderAddress)) {
    showToast(authStore.i18n("cm_addr.pleaseSelectAddress"));
    return;
  }
  pageData.submitLoading = false;
  const selectedSkuList = <any>[];
  const currentGoodsList = props.cartData?.goodsList || [];
  currentGoodsList.forEach((goods: any) => {
    goods.skuList.forEach((sku: any) => {
      if (sku.selected) {
        selectedSkuList.push({
          quantity: sku.buyQty,
          skuId: sku.skuId,
          spm: sku.spm,
          routeId: goods.routeId,
          padc: sku.padc,
        });
      }
    });
  });

  const res: any = await useFastCreateOrder({
    skuList: selectedSkuList,
    siteId: window.siteData.siteInfo.id,
    orderAddress: pageData.orderAddress,
  });
  if (res?.result?.code === 200) {
    navigateToPage(
      "/order/details",
      { orderNo: res.data.orderNo },
      false,
      event
    );
  } else if (res?.result?.code === 403) {
    emit("onGoLoginRegister");
  } else {
    pageData.errorDialogVisible = true;
    setTimeout(() => {
      pageData.errorDialogVisible = false;
    }, 3000);
    pageData.errorMessage =
      res?.result?.message || authStore.i18n("cm_find.errorMessage");
  }
  pageData.submitLoading = false;
}

// 获取国家列表
async function onGetCountry() {
  const res: any = await useGetCountry({});
  if (res?.result?.code === 200) {
    pageData.countryList = res?.data;
    // 设置秘鲁国家的默认数据
    const country = pageData.countryList.find(
      (item: any) => item.id === config.public.peruCountryId
    );
    if (country) {
      editForm.areaCode = country.areaCode;
      pageData.countryRegexes = country;

      if (isEmptyObject(pageData?.orderAddress)) {
        nextTick(() => {
          inlineAddressFormRef.value?.onInitAddrFormData();
        });
      }
    }
  }
}

// 地址选择相关函数
function onShowSelectAddress() {
  pageData.selectedAddress = pageData.orderAddress
    ? JSON.parse(JSON.stringify(pageData.orderAddress))
    : {};
  pageData.showSelectAddressModal = true;
}

function onCloseSelectAddress() {
  pageData.showSelectAddressModal = false;
  // 关闭时清空临时选择
  pageData.selectedAddress = {};
}

function onSelectAddress(address: any) {
  pageData.selectedAddress = address ? JSON.parse(JSON.stringify(address)) : {};
}

function onConfirmSelectAddress() {
  // 确认时才更新实际使用的地址
  pageData.orderAddress = pageData.selectedAddress;
  pageData.showSelectAddressModal = false;
  pageData.selectedAddress = {};
}

// 地址编辑相关函数
function onOpenAddAddr() {
  pageData.dialogVisible = true;
  nextTick(() => {
    modalAddressFormRef.value?.onInitAddrFormData();
  });
}

function onOpenEditAddr(address: any) {
  pageData.dialogVisible = true;
  nextTick(() => {
    modalAddressFormRef.value?.onInitAddrFormData(address);
  });
}

function onCloseAddAddr() {
  pageData.dialogVisible = false;
  Object.assign(editForm, {});
}

function onFormatPhone(address: any) {
  if (!address?.phone) return "";
  if (address.areaCode) {
    return `${address.areaCode} ${address.phone}`;
  }
  return address.phone;
}

// 删除地址相关函数
async function onConfirmDelete(address: any) {
  let params = {
    id: address?.id,
  };

  const isCurrentOrderAddress = pageData.orderAddress?.id === address?.id;
  const isCurrentSelectedAddress = pageData.selectedAddress?.id === address?.id;

  const res: any = await useDeleteUserAddress(params);
  if (res?.result?.code === 200) {
    // 重新获取地址列表
    const listRes: any = await useListUserAddress({});
    if (listRes?.result?.code === 200) {
      // 只保留秘鲁国家的地址
      const peruAddresses =
        listRes?.data?.filter(
          (item: any) => item.countryId === config.public.peruCountryId
        ) || [];

      pageData.addressList = peruAddresses;

      // 自动选择新地址的逻辑
      const selectNewAddress = () => {
        if (peruAddresses.length > 0) {
          // 优先选择默认地址
          const defaultAddress = peruAddresses.find(
            (item: any) => item.isDefault
          );
          return defaultAddress || peruAddresses[0]; // 没有默认地址则选第一条
        }
        return null; // 没有地址了
      };

      // 如果删除的是当前使用的地址，需要重新选择
      if (isCurrentOrderAddress) {
        const newAddress = selectNewAddress();
        pageData.orderAddress = newAddress || {};
      }

      // 如果删除的是模态框中选中的地址，也需要重新选择
      if (isCurrentSelectedAddress) {
        const newAddress = selectNewAddress();
        pageData.selectedAddress = newAddress
          ? JSON.parse(JSON.stringify(newAddress))
          : {};
      }
    }

    message.success("删除成功");
  } else {
    message.error(res.result?.message);
  }
}

async function onAddrToDefault(addr: any) {
  let params = {
    id: addr.id,
  };

  // 保存当前选中的地址ID，避免设置默认地址时被重置
  const currentSelectedAddressId = pageData.selectedAddress?.id;

  const res: any = await useAddressToDefault(params);
  if (res?.result?.code === 200) {
    // 重新获取地址列表
    const listRes: any = await useListUserAddress({});
    if (listRes?.result?.code === 200) {
      // 只保留秘鲁国家的地址
      const peruAddresses =
        listRes?.data?.filter(
          (item: any) => item.countryId === config.public.peruCountryId
        ) || [];

      pageData.addressList = peruAddresses;

      // 恢复之前选中的地址，如果该地址仍然存在
      if (currentSelectedAddressId) {
        const previousSelectedAddress = peruAddresses.find(
          (item: any) => item.id === currentSelectedAddressId
        );
        if (previousSelectedAddress) {
          // 地址仍然存在，恢复选中状态
          pageData.selectedAddress = JSON.parse(
            JSON.stringify(previousSelectedAddress)
          );
        } else {
          // 地址不存在了，选择新的默认地址或第一个地址
          const defaultAddress = peruAddresses.find(
            (item: any) => item.isDefault
          );
          pageData.selectedAddress = defaultAddress
            ? JSON.parse(JSON.stringify(defaultAddress))
            : peruAddresses.length > 0
            ? JSON.parse(JSON.stringify(peruAddresses[0]))
            : {};
        }
      } else {
        // 之前没有选中地址，选择默认地址或第一个地址
        const defaultAddress = peruAddresses.find(
          (item: any) => item.isDefault
        );
        pageData.selectedAddress = defaultAddress
          ? JSON.parse(JSON.stringify(defaultAddress))
          : peruAddresses.length > 0
          ? JSON.parse(JSON.stringify(peruAddresses[0]))
          : {};
      }
    }

    showToast(authStore.i18n("cm_addr.editSuccess"));
  } else {
    showToast(res.result?.message);
  }
}

function onUpdateListUserAddress(type?: any) {
  if (type === "inline") {
    onListUserAddress("init");
  } else {
    onListUserAddress();
    pageData.dialogVisible = false;
  }
}
</script>

<style scoped lang="scss">
.sku-checkbox {
  background: #f2f2f2;
  padding: 6px 4px 6px 10px;
  .sku-container {
    padding: 0 !important;
  }
}

:deep(.n-checkbox.n-checkbox--checked .n-checkbox-box) {
  background: #e50113;
}

:deep(.n-checkbox__label) {
  width: 100%;
  padding: 0 !important;
}

:deep(.divider .n-divider) {
  margin: 0px;
}

:deep(.n-divider__title) {
  margin-left: 8px !important;
  margin-right: 8px !important;
}

.divider.n-divider:not(.n-divider--vertical) {
  margin: 0px;
}

:deep(.divider .n-divider__line.n-divider__line--left) {
  display: none;
}

:deep(.default-checkbox.n-checkbox .n-checkbox-box) {
  width: 15px;
  height: 15px;
  border-radius: 50%;
}
</style>
